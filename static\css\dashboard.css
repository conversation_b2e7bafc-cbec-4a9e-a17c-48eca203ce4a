/* Navigation Styles */
nav {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 1rem 0;
    box-shadow: var(--shadow-md);
    border-bottom: 1px solid var(--gray-200);
}

nav .nav-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
    padding: 0 1rem;
}

nav a {
    color: var(--gray-600);
    text-decoration: none;
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: all 0.3s ease;
    background: white;
    border: 1px solid var(--gray-200);
    box-shadow: var(--shadow-sm);
}

nav a:hover {
    color: var(--primary-color);
    border-color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* Navigation Grid */
.navigation {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: var(--gray-100);
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--gray-200);
}

.navigation a {
    background: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-dark) 100%);
    color: white;
    padding: 1rem 1.5rem;
    text-decoration: none;
    border-radius: var(--border-radius);
    font-weight: 600;
    text-align: center;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-sm);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-size: 0.875rem;
}

.navigation a:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Actions Section */
.actions {
    margin-top: 2rem;
    padding: 1.5rem;
    background: var(--gray-100);
    border-radius: var(--border-radius-lg);
    text-align: center;
    border: 1px solid var(--gray-200);
}

/* Dashboard Stats */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    padding: 1.5rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    text-align: center;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-label {
    color: var(--gray-600);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-size: 0.875rem;
}

.stat-description {
    color: var(--gray-500);
    font-size: 0.75rem;
    margin-top: 0.25rem;
}

/* Progress Bars */
.progress-container {
    margin-top: 1rem;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--gray-200);
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border-radius: 4px;
    transition: width 0.3s ease;
}

.progress-text {
    font-size: 0.75rem;
    color: var(--gray-600);
    margin-top: 0.25rem;
    text-align: center;
}

/* Quick Actions */
.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 2rem;
}

.quick-action {
    background: white;
    padding: 1.5rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    text-align: center;
    transition: all 0.3s ease;
    text-decoration: none;
    color: var(--gray-800);
}

.quick-action:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    text-decoration: none;
    color: var(--primary-color);
}

.quick-action-icon {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.quick-action-title {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.quick-action-description {
    font-size: 0.875rem;
    color: var(--gray-600);
}

/* Recent Activity */
.recent-activity {
    background: white;
    padding: 1.5rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    margin-top: 2rem;
}

.activity-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--gray-200);
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--gray-100);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
}

.activity-content {
    flex: 1;
}

.activity-title {
    font-weight: 500;
    color: var(--gray-800);
    margin-bottom: 0.25rem;
}

.activity-time {
    font-size: 0.75rem;
    color: var(--gray-500);
}

/* Responsive Design */
@media (max-width: 768px) {
    .navigation {
        grid-template-columns: 1fr;
    }

    nav .nav-container {
        flex-direction: column;
        gap: 0.5rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .quick-actions {
        grid-template-columns: 1fr;
    }

    .activity-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
}
