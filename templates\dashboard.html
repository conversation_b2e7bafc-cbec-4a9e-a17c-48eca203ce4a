<!DOCTYPE html>
<html>
<head>
    <title>Meme Content Generator</title>
    <link rel="stylesheet" href="/static/css/base.css">
    <link rel="stylesheet" href="/static/css/dashboard.css">
</head>
<body>
    <nav>
        <div class="nav-container">
            <a href="/dashboard">🏠 Home</a>
            <a href="/fetch_memes">🎭 Fetch Memes</a>
            <a href="/config">⚙️ Settings</a>
            <a href="/logout">🚪 Logout</a>
        </div>
    </nav>
    <div class="container">
        <!-- Flash Messages -->
        {% if session.flash_message %}
        <div style="background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 6px; margin: 20px 0;">
            {{ session.flash_message }}
            {% set _ = session.pop('flash_message') %}
        </div>
        {% endif %}

        <!-- Statistics Overview -->
        <h2>📊 Content Statistics</h2>
        <div class="stats-grid">
            <div class="stat-card info">
                <div class="stat-number">{{ stats.total_memes }}</div>
                <div class="stat-label">Total Memes</div>
                <div class="stat-description">Memes fetched and ready</div>
            </div>

            <div class="stat-card {% if stats.memes_need_text > 0 %}need-attention{% else %}success{% endif %}">
                <div class="stat-number">{{ stats.memes_need_text }}</div>
                <div class="stat-label">Need Text</div>
                <div class="stat-description">Memes waiting for text</div>
            </div>

            <div class="stat-card success">
                <div class="stat-number">{{ stats.memes_with_text }}</div>
                <div class="stat-label">Ready for Videos</div>
                <div class="stat-description">Memes with text added</div>
            </div>

            <div class="stat-card info">
                <div class="stat-number">{{ stats.total_videos }}</div>
                <div class="stat-label">Videos Created</div>
                <div class="stat-description">Generated video content</div>
            </div>

            <div class="stat-card">
                <div class="stat-number">{{ stats.memes_today }}</div>
                <div class="stat-label">Memes Today</div>
                <div class="stat-description">Fetched today</div>
            </div>

            <div class="stat-card">
                <div class="stat-number">{{ stats.videos_today }}</div>
                <div class="stat-label">Videos Today</div>
                <div class="stat-description">Created today</div>
            </div>
        </div>

        <!-- Quick Actions -->
        <h2>🚀 Quick Actions</h2>
        <div class="quick-actions">
            {% if stats.total_memes == 0 %}
                <a href="/fetch_memes" class="quick-action">
                    <div class="quick-action-icon">🎭</div>
                    <div class="quick-action-title">Fetch Memes</div>
                    <div class="quick-action-description">Get memes from Reddit</div>
                </a>
                <a href="/config" class="quick-action">
                    <div class="quick-action-icon">⚙️</div>
                    <div class="quick-action-title">Configure Settings</div>
                    <div class="quick-action-description">Set up your preferences</div>
                </a>
            {% elif stats.memes_need_text > 0 %}
                <a href="/review_memes" class="quick-action">
                    <div class="quick-action-icon">✏️</div>
                    <div class="quick-action-title">Add Text to {{ stats.memes_need_text }} Memes</div>
                    <div class="quick-action-description">Add narration text</div>
                </a>
                <a href="/fetch_memes" class="quick-action">
                    <div class="quick-action-icon">🎭</div>
                    <div class="quick-action-title">Fetch More Memes</div>
                    <div class="quick-action-description">Get more content</div>
                </a>
            {% elif stats.memes_with_text > 0 and stats.total_videos == 0 %}
                <a href="/generate_videos" class="quick-action">
                    <div class="quick-action-icon">🎬</div>
                    <div class="quick-action-title">Generate Videos</div>
                    <div class="quick-action-description">Create video content</div>
                </a>
                <a href="/review_memes" class="quick-action">
                    <div class="quick-action-icon">✏️</div>
                    <div class="quick-action-title">Review Memes</div>
                    <div class="quick-action-description">Edit meme text</div>
                </a>
            {% else %}
                <a href="/review_videos" class="quick-action">
                    <div class="quick-action-icon">📹</div>
                    <div class="quick-action-title">Review Videos</div>
                    <div class="quick-action-description">View created videos</div>
                </a>
                <a href="/generate_videos" class="quick-action">
                    <div class="quick-action-icon">🎬</div>
                    <div class="quick-action-title">Generate More Videos</div>
                    <div class="quick-action-description">Create more content</div>
                </a>
                <a href="/fetch_memes" class="quick-action">
                    <div class="quick-action-icon">🎭</div>
                    <div class="quick-action-title">Fetch More Memes</div>
                    <div class="quick-action-description">Get more memes</div>
                </a>
            {% endif %}
            <a href="/config" class="quick-action">
                <div class="quick-action-icon">⚙️</div>
                <div class="quick-action-title">Settings</div>
                <div class="quick-action-description">Configure app</div>
            </a>
            {% if stats.total_memes > 0 %}
            <form method="POST" action="/clear_memes" style="display: contents;">
                <button type="submit" class="quick-action btn-danger" onclick="return confirm('Are you sure you want to clear all memes? This cannot be undone.')">
                    <div class="quick-action-icon">🗑️</div>
                    <div class="quick-action-title">Clear All Memes</div>
                    <div class="quick-action-description">Remove all content</div>
                </button>
            </form>
            {% endif %}
        </div>

        <!-- Workflow Status -->
        {% if stats.total_memes > 0 %}
        <h2>📈 Workflow Progress</h2>
        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                <span><strong>Content Pipeline Status</strong></span>
                <span>{{ ((stats.total_videos / stats.total_memes) * 100) | round(1) if stats.total_memes > 0 else 0 }}% Complete</span>
            </div>
            <div style="background: #e9ecef; height: 20px; border-radius: 10px; overflow: hidden;">
                <div style="background: #28a745; height: 100%; width: {{ ((stats.total_videos / stats.total_memes) * 100) if stats.total_memes > 0 else 0 }}%; transition: width 0.3s;"></div>
            </div>
            <div style="margin-top: 10px; font-size: 0.9em; color: #6c757d;">
                {{ stats.total_memes }} memes → {{ stats.memes_with_text }} with text → {{ stats.total_videos }} videos
            </div>
        </div>
        {% endif %}

        <!-- Additional Stats -->
        {% if stats.discarded_memes > 0 or stats.discarded_videos > 0 %}
        <h2>🗑️ Discarded Content</h2>
        <div class="stats-grid">
            {% if stats.discarded_memes > 0 %}
            <div class="stat-card">
                <div class="stat-number">{{ stats.discarded_memes }}</div>
                <div class="stat-label">Discarded Memes</div>
                <div class="stat-description">Removed from workflow</div>
            </div>
            {% endif %}
            {% if stats.discarded_videos > 0 %}
            <div class="stat-card">
                <div class="stat-number">{{ stats.discarded_videos }}</div>
                <div class="stat-label">Discarded Videos</div>
                <div class="stat-description">Removed from workflow</div>
            </div>
            {% endif %}
        </div>
        {% endif %}
    </div>
</body>
</html>
