<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Meme Video Generator - AI-Powered Content Creation</title>
    <link rel="stylesheet" href="/static/css/base.css">
    <link rel="stylesheet" href="/static/css/login.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
</head>
<body class="landing-page">
    <!-- Hero Section -->
    <div class="hero-section">
        <div class="hero-background">
            <div class="floating-shapes">
                <div class="shape shape-1"></div>
                <div class="shape shape-2"></div>
                <div class="shape shape-3"></div>
                <div class="shape shape-4"></div>
            </div>
        </div>

        <div class="hero-content">
            <div class="hero-text">
                <h1 class="hero-title">
                    <span class="gradient-text">AI-Powered</span><br>
                    Meme Video Generator
                </h1>
                <p class="hero-subtitle">
                    Transform memes into engaging videos with AI narration.<br>
                    Fetch from Reddit, add text, generate audio, and create viral content.
                </p>

                <div class="features-preview">
                    <div class="feature-item">
                        <span class="feature-icon">🎭</span>
                        <span>Reddit Integration</span>
                    </div>
                    <div class="feature-item">
                        <span class="feature-icon">🎵</span>
                        <span>AI Voice Generation</span>
                    </div>
                    <div class="feature-item">
                        <span class="feature-icon">🎬</span>
                        <span>Video Creation</span>
                    </div>
                </div>
            </div>

            <div class="login-card">
                <div class="login-header">
                    <h2>Get Started</h2>
                    <p>Sign in to create amazing meme videos</p>
                </div>

                {% if error %}
                <div class="error-message">
                    <p>{{ error }}</p>
                </div>
                {% endif %}

                <form method="POST" action="/login" class="login-form">
                    <div class="form-group">
                        <label for="username">Email</label>
                        <input type="email" id="username" name="username" placeholder="Enter your email" required>
                    </div>

                    <div class="form-group">
                        <label for="password">Password</label>
                        <input type="password" id="password" name="password" placeholder="Enter your password" required>
                    </div>

                    <button type="submit" class="login-btn">
                        <span>Sign In</span>
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M5 12h14M12 5l7 7-7 7"/>
                        </svg>
                    </button>
                </form>

                <div class="demo-credentials">
                    <p>Demo Credentials:</p>
                    <code><EMAIL></code>
                    <code>dhanush21feb@Amcg</code>
                </div>
            </div>
        </div>
    </div>

    <!-- Features Section -->
    <div class="features-section">
        <div class="container">
            <h2 class="section-title">Powerful Features</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon-large">🎭</div>
                    <h3>Reddit Integration</h3>
                    <p>Automatically fetch trending memes from multiple subreddits with customizable filters.</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon-large">✏️</div>
                    <h3>Text Editor</h3>
                    <p>Add custom text to memes with an intuitive editor and real-time preview.</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon-large">🎵</div>
                    <h3>AI Voice Generation</h3>
                    <p>Convert text to natural-sounding speech with multiple voice options.</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon-large">🎬</div>
                    <h3>Video Creation</h3>
                    <p>Combine memes with AI-generated audio to create engaging video content.</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon-large">📱</div>
                    <h3>Easy Management</h3>
                    <p>Organize, preview, and download your created content with a beautiful interface.</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon-large">⚡</div>
                    <h3>Fast & Reliable</h3>
                    <p>Optimized performance with persistent storage and efficient processing.</p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
