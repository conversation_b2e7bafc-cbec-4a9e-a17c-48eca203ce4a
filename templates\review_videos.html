<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Review Videos</title>
    <link rel="stylesheet" href="/static/css/base.css">
    <link rel="stylesheet" href="/static/css/dashboard.css">
    <link rel="stylesheet" href="/static/css/videos.css">
</head>
<body>
    <header>
        <h1>Review Videos</h1>
    </header>
    <nav>
        <div class="nav-container">
            <a href="/dashboard">🏠 Home</a>
            <a href="/fetch_memes">🎭 Fetch Memes</a>
            <a href="/config">⚙️ Settings</a>
            <a href="/logout">🚪 Logout</a>
        </div>
    </nav>
    <div class="container">
        {% if videos %}
            {% for video in videos %}
            <div class="video-item">
                <div class="video-player">
                    <video controls style="max-width: 600px; max-height: 400px;">
                        <source src="/{{ video.video_path }}" type="video/mp4">
                        Your browser does not support the video tag.
                    </video>
                </div>
                <div class="video-info">
                    <p><strong>Text:</strong> {{ video.text }}</p>
                    <p><strong>Created:</strong> {{ video.created_at.strftime('%Y-%m-%d %H:%M') if video.created_at else 'Unknown' }}</p>
                    <div class="video-actions">
                        <form method="POST" action="/discard_video" style="display: inline;">
                            <input type="hidden" name="video_id" value="{{ video.id }}">
                            <button type="submit" class="btn btn-danger"
                                    onclick="return confirm('Are you sure you want to discard this video?')">🗑️ Discard</button>
                        </form>
                        {% if video.audio_path %}
                        <a href="/{{ video.audio_path }}" download class="btn btn-secondary">🎵 Download Audio</a>
                        {% endif %}
                        <a href="/{{ video.video_path }}" download class="btn btn-primary">📹 Download Video</a>
                    </div>
                </div>
            </div>
            <hr>
            {% endfor %}

            <div class="actions">
                <a href="/generate_videos" class="btn btn-secondary">🎬 Generate More Videos</a>
                <a href="/dashboard" class="btn btn-primary">🏠 Back to Dashboard</a>
            </div>
        {% else %}
            <p>No videos generated yet. <a href="/review_memes" class="btn btn-primary">✏️ Add text to memes</a> and then <a href="/generate_videos" class="btn btn-secondary">🎬 generate videos</a>!</p>
        {% endif %}
    </div>
</body>
</html>
